<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" min="0" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Oauth_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-oauth_type" data-rule="required" class="form-control selectpicker" name="row[oauth_type]">
                {foreach name="oauthTypeList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="$row.oauth_type"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Oauth_value')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-oauth_value" data-rule="required" class="form-control" name="row[oauth_value]" type="text" value="{$row.oauth_value|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
