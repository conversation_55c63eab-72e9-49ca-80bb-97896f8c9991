<?php

namespace app\admin\model\wevote;

use think\Model;


class Oauth extends Model
{





    // 表名
    protected $name = 'wevote_oauth';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'oauth_type_text'
    ];



    public function getOauthTypeList()
    {
        return ['openid' => __('Oauth_type openid'), 'unionid' => __('Oauth_type unionid')];
    }


    public function getOauthTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['oauth_type'] ?? '');
        $list = $this->getOauthTypeList();
        return $list[$value] ?? '';
    }




}
