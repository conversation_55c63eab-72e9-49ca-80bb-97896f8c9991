#!/bin/bash

# 1. 提示用户输入插件名称
read -p "请输入插件名称: " plugin

if [ -z "$plugin" ]; then
  echo "插件名称不能为空"
  exit 1
fi

# 2. 定义路径数组
src_paths=(
  "public/assets/js/backend/$plugin"
  "public/assets/addons/$plugin"
  "application/admin/view/$plugin"
  "application/admin/validate/$plugin"
  "application/admin/model/$plugin"
  "application/admin/lang/zh-cn/$plugin"
  "application/admin/controller/$plugin"
)

dest_paths=(
  "addons/$plugin/public/assets/js/backend/$plugin"
  "addons/$plugin/assets"
  "addons/$plugin/application/admin/view/$plugin"
  "addons/$plugin/application/admin/validate/$plugin"
  "addons/$plugin/application/admin/model/$plugin"
  "addons/$plugin/application/admin/lang/zh-cn/$plugin"
  "addons/$plugin/application/admin/controller/$plugin"
)

# 3. 执行复制
echo "开始复制目录..."
for ((i = 0; i < ${#src_paths[@]}; i++)); do
  src="${src_paths[$i]}"
  dest="${dest_paths[$i]}"
  echo "  复制: $src -> $dest"
  mkdir -p "$(dirname "$dest")"
  cp -r "$src" "$dest"
done

# 4. 打包
echo "执行插件打包命令: php think addon -a $plugin -c package"
php think addon -a "$plugin" -c package

# 5. 删除目标路径及其空的上层目录
echo "打包完成，开始清理目录..."

# 定义安全删除的根目录边界
SAFE_ROOTS=("addons/$plugin")

delete_with_cleanup() {
  path="$1"
  echo "  删除: $path"
  rm -rf "$path"

  # 回溯删除空目录直到安全边界
  dir=$(dirname "$path")
  while [[ "$dir" != "." ]]; do
    # 检查是否达到了安全边界
    for safe in "${SAFE_ROOTS[@]}"; do
      if [[ "$dir" == "$safe" ]]; then
        return
      fi
    done

    # 如果目录存在且为空，则删除
    if [ -d "$dir" ] && [ -z "$(ls -A "$dir")" ]; then
      echo "    清理空目录: $dir"
      rmdir "$dir"
      dir=$(dirname "$dir")
    else
      return
    fi
  done
}

# 执行删除和清理
for dest in "${dest_paths[@]}"; do
  delete_with_cleanup "$dest"
done

echo "所有操作完成。"
