# wevote插件开发基座
项目用于开发在fastadmin.net上架的wevote插件的开发。 git仓库包含两个分支：mian与pack
- main分支用于日常开发
- pack分支用于打包发布到fastadmin.net上架
## 工作流
1. 在`/Users/<USER>/Workspace/project/frontend/wevote/uniapp/wevote`目录下进行uniapp客户端开发、测试以及演示项目的打包与发布，测试完成后commit。
2. 将前端项目clone到`addons/wevote/uniapp`目录下
3. 在main分支下进行后端开发，开发完成后commit
4. 切换到pack分钟，使用cherry-pick将main分支的commit合并到pack分支
5. 执行`./pack_addon.sh`脚本，将插件打包到`addons/wevote`目录下

## 演示站点
演示站点位于demo分支，演示站点主要不要通过后台安装插件，因为插件控制器使用了特殊的继承类处理写入请求。同时关闭了文件上传等功能，具体可以查看demo分支提交内容。
