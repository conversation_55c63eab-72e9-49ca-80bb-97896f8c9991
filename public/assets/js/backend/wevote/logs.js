define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'wevote/logs/index' + location.search,
                    add_url: 'wevote/logs/add',
                    edit_url: 'wevote/logs/edit',
                    del_url: 'wevote/logs/del',
                    multi_url: 'wevote/logs/multi',
                    import_url: 'wevote/logs/import',
                    table: 'wevote_logs',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'user_id', title: __('User_id')},
                        {field: 'vote_id', title: __('Vote_id')},
                        {field: 'option_id', title: __('Option_id')},
                        {field: 'vote_date', title: __('Vote_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'ip_address', title: __('Ip_address'), operate: 'LIKE'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
