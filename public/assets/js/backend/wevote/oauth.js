define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

  var Controller = {
    index: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          index_url: 'wevote/oauth/index' + location.search,
          add_url: 'wevote/oauth/add',
          edit_url: 'wevote/oauth/edit',
          del_url: 'wevote/oauth/del',
          multi_url: 'wevote/oauth/multi',
          import_url: 'wevote/oauth/import',
          table: 'wevote_oauth',
        }
      });

      var table = $("#table");

      // 初始化表格
      table.bootstrapTable({
        url: $.fn.bootstrapTable.defaults.extend.index_url,
        pk: 'id',
        sortName: 'id',
        columns: [
          [
            {checkbox: true},
            {field: 'id', title: __('Id')},
            {field: 'user_id', title: __('User_id')},
            {
              field: 'oauth_type',
              title: __('Oauth_type'),
              searchList: {"openid": __('Oauth_type openid'), "unionid": __('Oauth_type unionid')},
              formatter: Table.api.formatter.normal
            },
            {
              field: 'oauth_value',
              title: __('Oauth_value'),
              operate: 'LIKE',
              table: table,
              class: 'autocontent',
              formatter: Table.api.formatter.content
            },
            {
              field: 'createtime',
              title: __('Createtime'),
              operate: 'RANGE',
              addclass: 'datetimerange',
              autocomplete: false,
              formatter: Table.api.formatter.datetime
            },
            {
              field: 'updatetime',
              title: __('Updatetime'),
              operate: 'RANGE',
              addclass: 'datetimerange',
              autocomplete: false,
              formatter: Table.api.formatter.datetime
            },
            {
              field: 'operate',
              title: __('Operate'),
              table: table,
              events: Table.api.events.operate,
              formatter: Table.api.formatter.operate
            }
          ]
        ]
      });

      // 为表格绑定事件
      Table.api.bindevent(table);
    },
    add: function () {
      Controller.api.bindevent();
    },
    edit: function () {
      Controller.api.bindevent();
    },
    api: {
      bindevent: function () {
        Form.api.bindevent($("form[role=form]"));
      }
    }
  };
  return Controller;
});
